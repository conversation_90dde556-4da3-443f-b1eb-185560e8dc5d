# Tutorial TP-Link TL-ER604W para Entorno Educativo Moodle

## Información del Proyecto
- **Router**: TP-Link TL-ER604W
- **Entorno**: Servidor Ubuntu con Moodle
- **Usuarios**: 6 grupos de 30 estudiantes (180 total), con posibilidad de 70+ simultáneos
- **Objetivo**: Control de acceso a internet con lista blanca y funciones avanzadas

## Características del TP-Link TL-ER604W

### Especificaciones Técnicas
- **Tipo**: Router empresarial de banda ancha
- **Puertos WAN**: 4 puertos WAN Gigabit
- **Puertos LAN**: 1 puerto LAN Gigabit
- **Usuarios simultáneos**: Hasta 500+ usuarios
- **VPN**: Soporte para múltiples túneles VPN
- **Balanceador de carga**: Sí
- **Firewall**: Avanzado con control de acceso

## 1. Configuración Básica

### Acceso al Panel de Administración
1. Conectar por cable al puerto LAN
2. Abrir navegador: `http://***********` (IP por defecto)
3. Usuario: `admin` / Contraseña: `admin` (cambiar inmediatamente)

### Configuración Inicial de Red
```
WAN1: Conexión principal a internet
WAN2-4: Conexiones adicionales (si disponibles)
LAN: Red interna (***********/24)
```

## 2. Lista Blanca de Sitios Web (Web Content Filtering)

### Configuración de Filtrado de Contenido
1. **Advanced → Access Control → Web Content Filtering**
2. **Crear política de filtrado**:
   - Nombre: "Estudiantes_Whitelist"
   - Acción: "Permit" (solo permitir)
   - Aplicar a: Todos los usuarios

### Sitios Permitidos (Lista Blanca)
```
# Sitios educativos esenciales
*.moodle.org
*.wikipedia.org
*.edu.co
*.gov.co

# Tu servidor Moodle local
***********00  # (ajustar según tu IP del servidor)
localhost
tu-dominio-moodle.local

# Recursos educativos
*.khanacademy.org
*.coursera.org
*.edx.org
*.google.com/scholar
scholar.google.com

# Herramientas básicas
translate.google.com
drive.google.com
docs.google.com
```

### Configuración Paso a Paso
1. **Advanced → Access Control → Domain Filtering**
2. **Enable Domain Filtering**: ✓
3. **Default Action**: Deny (denegar todo por defecto)
4. **Add Domain Rules**:
   - Action: Allow
   - Domain: Agregar cada dominio de la lista

## 3. Gestión de Ancho de Banda

### Configuración para 70+ Usuarios Simultáneos
1. **Advanced → Load Balancing → Bandwidth Control**
2. **Configurar límites por usuario**:
   - Download: 2 Mbps por usuario
   - Upload: 1 Mbps por usuario
   - Garantizar: 512 Kbps mínimo

### Priorización de Tráfico (QoS)
```
Prioridad Alta: Moodle, SSH, DNS
Prioridad Media: HTTP/HTTPS educativo
Prioridad Baja: Otros servicios
```

## 4. Configuraciones Avanzadas

### A. Múltiples Redes (VLANs Virtuales)
- **Red Profesores**: ***********/24 (acceso completo)
- **Red Estudiantes**: ***********/24 (acceso restringido)
- **Red Administración**: ***********/24 (solo admin)

### B. Portal Cautivo (Captive Portal)
1. **Advanced → Access Control → Portal**
2. Configurar página de bienvenida
3. Términos de uso obligatorios
4. Autenticación por sesión

### C. Programación de Acceso
1. **Advanced → Access Control → Time Range**
2. **Horarios de clase**:
   - Lunes a Viernes: 7:00 AM - 6:00 PM
   - Sábados: 8:00 AM - 2:00 PM
   - Domingos: Sin acceso

### D. Monitoreo y Logs
1. **Advanced → System Tools → Log**
2. **Configurar servidor Syslog** (opcional)
3. **Monitoreo en tiempo real**:
   - Status → Traffic Statistics
   - Status → Online Users

## 5. Balanceador de Carga (Load Balancing)

### Configuración Multi-WAN
Si tienes múltiples conexiones a internet:
1. **Advanced → Load Balancing → Load Balancing**
2. **Modo**: Bandwidth Based Routing
3. **Distribución**: 
   - WAN1: 60% (conexión principal)
   - WAN2: 40% (conexión secundaria)

### Failover Automático
- **Link Backup**: Activar
- **Detección**: Ping a *******
- **Intervalo**: 10 segundos

## 6. Seguridad Avanzada

### Firewall Rules
```
# Bloquear redes sociales
DENY facebook.com, instagram.com, twitter.com, tiktok.com

# Bloquear streaming
DENY youtube.com (excepto canales educativos específicos)
DENY netflix.com, spotify.com

# Bloquear juegos
DENY steam.com, epicgames.com, battle.net
```

### Prevención de Intrusiones
1. **Advanced → Security → Attack Defense**
2. **DoS Attack Defense**: Enable
3. **IP/MAC Binding**: Para dispositivos críticos

## 7. Configuración para Moodle

### Port Forwarding para Moodle
1. **Advanced → NAT → Virtual Server**
2. **Configurar**:
   - Service Port: 80, 443
   - Internal Port: 80, 443
   - IP Address: [IP del servidor Moodle]

### Optimización para Moodle
- **Session Limit**: 200 sesiones simultáneas
- **Connection Limit**: 50 conexiones por IP
- **Timeout**: 30 minutos

## 8. Monitoreo y Mantenimiento

### Dashboard de Monitoreo
- **Usuarios conectados**: Status → Online Users
- **Uso de ancho de banda**: Status → Traffic Statistics
- **Estado de WAN**: Status → WAN Status

### Alertas Automáticas
- **Email notifications** para:
  - Conexión WAN caída
  - Uso excesivo de ancho de banda
  - Intentos de acceso no autorizados

### Backup de Configuración
1. **System Tools → Backup & Restore**
2. **Backup automático**: Semanal
3. **Guardar en**: USB o servidor FTP

## 9. Solución de Problemas Comunes

### Problema: Lentitud con muchos usuarios
**Solución**: 
- Reducir límite de ancho de banda por usuario
- Activar cache DNS local
- Optimizar reglas de firewall

### Problema: Sitios bloqueados incorrectamente
**Solución**:
- Revisar Domain Filtering rules
- Agregar excepciones específicas
- Verificar logs de acceso

### Problema: Conexión intermitente
**Solución**:
- Verificar cables de red
- Revisar configuración de Load Balancing
- Actualizar firmware del router

## 10. Próximas Actualizaciones

### Funciones Pendientes de Configurar
- [ ] Integración con Active Directory
- [ ] Configuración de VPN para acceso remoto
- [ ] Implementación de guest network
- [ ] Configuración de servidor RADIUS

---

**Última actualización**: 2025-08-01
**Versión**: 1.0
**Estado**: Configuración inicial completada
