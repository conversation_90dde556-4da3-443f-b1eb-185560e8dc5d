# Tutorial TP-Link TL-ER604W para Entorno Educativo Moodle

## Información del Proyecto
- **Router**: TP-Link TL-ER604W SafeStream Wireless N Gigabit Broadband VPN Router
- **Entorno**: Servidor Ubuntu con Moodle - ICFES Matemáticas
- **Profesor**: Un solo profesor administrador
- **SSID**: "icfesmatematicas1" (red única para todos)
- **Usuarios**: 6 grupos de 30 estudiantes (180 total), con posibilidad de 70+ simultáneos
- **Objetivo**: Control de acceso a internet con lista blanca y supervisión simplificada

## Características Reales del TP-Link TL-ER604W

### Especificaciones Técnicas Oficiales
- **Tipo**: SafeStream Wireless N Gigabit Broadband VPN Router
- **Puertos**: 1 WAN Gigabit + 3 LAN Gigabit + 1 WAN/LAN Gigabit configurable
- **WiFi**: 802.11n 300Mbps (2.4GHz) con 2 antenas 5dBi desmontables
- **Sesiones simultáneas**: 10,000 sesiones concurrentes
- **NAT Throughput**: 160 Mbps
- **VPN**: 30 túneles IPsec + 8 PPTP + 8 L2TP simultáneos
- **IPsec VPN Throughput**: 38 Mbps
- **Multi-SSID**: Hasta 8 SSIDs diferentes
- **Memoria**: 64MB DDRII RAM + 8MB Flash
- **Alimentación**: 12VDC/1.0A (adaptador externo)
- **Protección**: 4KV contra rayos

## 1. Configuración Básica

### Acceso al Panel de Administración
1. Conectar por cable al puerto LAN
2. Abrir navegador: `http://***********` (IP por defecto)
3. Usuario: `admin` / Contraseña: `admin` (**CAMBIAR INMEDIATAMENTE**)

### Configuración Inicial de Red
```
Puerto WAN: Conexión principal a internet
Puertos LAN 1-3: Dispositivos cableados
Puerto WAN/LAN: Configurable según necesidad
WiFi: 300Mbps 802.11n (hasta 8 SSIDs)
```

### Funciones de Seguridad Disponibles
- **Application Control**: IM, P2P, Web IM, Web SNS, Web Media
- **Filtering**: MAC, URL/Keywords, Web Content (Java, ActiveX, Cookies)
- **Attack Defense**: TCP/UDP/ICMP Flood, TCP Scan, Ping Block
- **ARP Inspection**: GARP Packets, ARP Scanning, IP-MAC Binding

## 2. Lista Blanca de Sitios Web (Web Content Filtering)

### Configuración de Filtrado de Contenido
1. **Advanced → Access Control → Web Content Filtering**
2. **Crear política de filtrado**:
   - Nombre: "Estudiantes_Whitelist"
   - Acción: "Permit" (solo permitir)
   - Aplicar a: Todos los usuarios

### Control de Aplicaciones (Application Control)
El TL-ER604W incluye control nativo de:
- **IM**: Mensajería instantánea
- **P2P**: Torrents y descargas P2P
- **Web IM**: WhatsApp Web, Telegram Web
- **Web SNS**: Facebook, Twitter, Instagram
- **Web Media**: YouTube, streaming

### Sitios Permitidos (Lista Blanca)
```
# Sitios educativos esenciales
*.moodle.org
*.wikipedia.org
*.edu.co
*.gov.co

# Tu servidor Moodle local
*************  # (ajustar según tu IP del servidor)
localhost
tu-dominio-moodle.local

# Recursos educativos
*.khanacademy.org
*.coursera.org
*.edx.org
*.google.com/scholar
scholar.google.com

# Herramientas básicas
translate.google.com
drive.google.com
docs.google.com
```

### Configuración Paso a Paso
1. **Advanced → Access Control → Domain Filtering**
2. **Enable Domain Filtering**: ✓
3. **Default Action**: Deny (denegar todo por defecto)
4. **Add Domain Rules**:
   - Action: Allow
   - Domain: Agregar cada dominio de la lista

## 3. Gestión de Ancho de Banda y Tráfico

### Capacidad Real del Router
- **Sesiones simultáneas**: 10,000 (más que suficiente para 70+ usuarios)
- **NAT Throughput**: 160 Mbps total
- **Recomendación**: 2-3 Mbps por usuario para navegación fluida

### Configuración de Control de Ancho de Banda
1. **Advanced → Traffic Control → Bandwidth Control**
2. **Configurar límites por IP/rango**:
   - **Guarantee Bandwidth**: 1 Mbps mínimo por usuario
   - **Limited Bandwidth**: 3 Mbps máximo por usuario
   - **Time-scheduled Policy**: Horarios de clase

### Control de Sesiones
1. **Advanced → Traffic Control → Session Limit**
2. **Configurar límites**:
   - **IP-based Session Limit**: 50 sesiones por IP
   - **Previene**: Saturación por descargas masivas

## 4. Configuraciones Avanzadas Reales

### A. Configuración SSID Principal "icfesmatematicas1"
1. **Advanced → Wireless → Basic Settings**
2. **Configuración actual**:
   - **SSID**: "icfesmatematicas1" (ya configurado)
   - **Security**: WPA2-PSK (recomendado)
   - **Channel**: Auto o canal fijo (1, 6, 11)
   - **Transmission Power**: 100% (cobertura máxima)

### B. Configuración Simplificada para Un Solo Profesor
**Ventajas de un solo SSID**:
- Control centralizado más fácil
- Monitoreo simplificado de todos los usuarios
- Configuración de reglas única para todos
- Sin complejidad de múltiples redes

### C. Guest Network (Opcional para Visitantes)
Si necesitas acceso para visitantes ocasionales:
1. **Advanced → Wireless → Guest Network**
2. **Configurar**:
   - **SSID**: "icfes-invitados"
   - Acceso solo a internet (sin Moodle interno)
   - Límite de tiempo: 2 horas
   - Ancho de banda: 1 Mbps máximo

### D. Programación de Acceso por Tiempo
1. **Advanced → Access Control → Time Range**
2. **Crear horario único**:
   - **Horario-ICFES**: L-V 7:00-18:00, S 8:00-14:00
   - **Acceso-Profesor**: Siempre disponible (IP fija del profesor)
   - **Mantenimiento**: D 2:00-6:00 (solo profesor)

### E. Monitoreo Simplificado para Un Profesor
1. **Status → Online Users**: Ver quién está conectado a "icfesmatematicas1"
2. **Status → Traffic Statistics**: Monitorear uso total de ancho de banda
3. **Advanced → System Tools → Log**: Ver intentos de acceso bloqueados
4. **Dashboard principal**: Resumen de estado en una sola pantalla

### F. Configuración de IP Fija para el Profesor
1. **Advanced → DHCP → Address Reservation**
2. **Reservar IP fija**:
   - **MAC Address**: [MAC de tu dispositivo]
   - **Reserved IP**: ************* (ejemplo)
   - **Description**: "Profesor-ICFES"
   - **Beneficio**: Siempre la misma IP para reglas específicas

## 5. Funciones VPN Avanzadas

### Capacidades VPN del TL-ER604W
- **IPsec VPN**: 30 túneles simultáneos (LAN-to-LAN y Client-to-LAN)
- **PPTP VPN**: 8 túneles con cifrado MPPE
- **L2TP VPN**: 8 túneles con L2TP over IPsec
- **VPN Throughput**: 38 Mbps para IPsec

### Configuración VPN para Acceso Remoto
1. **Advanced → VPN → IPsec VPN**
2. **Para profesores remotos**:
   - Crear túnel Client-to-LAN
   - Cifrado: AES256
   - Autenticación: SHA1
   - Acceso completo a Moodle

### Configuración Site-to-Site
1. **Advanced → VPN → IPsec VPN**
2. **Para conectar sedes**:
   - Modo: LAN-to-LAN
   - Negociación: Main Mode
   - Perfect Forward Secrecy (PFS): Activado

## 6. Seguridad Avanzada Integrada

### Attack Defense (Defensa contra Ataques)
1. **Advanced → Security → Attack Defense**
2. **Activar protecciones**:
   - **TCP/UDP/ICMP Flood Defense**: Contra ataques DDoS
   - **Block TCP Scan**: Stealth FIN/Xmas/Null
   - **Block Ping from WAN**: Ocultar router desde internet
   - **CSRF Defense**: Protección contra falsificación de solicitudes

### Control de Aplicaciones (Un Clic)
1. **Advanced → Access Control → Application Control**
2. **Bloquear con un clic**:
   - **IM**: WhatsApp, Telegram, Skype
   - **P2P**: BitTorrent, eMule, Ares
   - **Web IM**: Versiones web de mensajería
   - **Web SNS**: Facebook, Instagram, Twitter
   - **Web Media**: YouTube, Netflix, Spotify

### IP-MAC Binding (Seguridad de Red)
1. **Advanced → Security → ARP Inspection**
2. **Configurar**:
   - **IP-MAC Binding**: Asociar IPs fijas a MACs específicas
   - **ARP Scanning**: Detectar dispositivos no autorizados
   - **GARP Packets**: Prevenir suplantación ARP

## 7. Configuración Específica para Moodle

### Port Forwarding para Servidor Moodle
1. **Advanced → NAT → Virtual Server**
2. **Configurar reglas**:
   - **HTTP**: External Port 80 → Internal Port 80 → IP Servidor Moodle
   - **HTTPS**: External Port 443 → Internal Port 443 → IP Servidor Moodle
   - **SSH**: External Port 22 → Internal Port 22 → IP Servidor Moodle (opcional)

### Optimización para 70+ Usuarios en Moodle
1. **Advanced → Traffic Control → Session Limit**
2. **Configurar límites**:
   - **Per IP Session Limit**: 100 sesiones por IP
   - **Total Session Limit**: 5000 sesiones totales
   - **Timeout**: 1800 segundos (30 minutos)

### Priorización de Tráfico Moodle
1. **Advanced → Traffic Control → Bandwidth Control**
2. **Crear reglas de prioridad**:
   - **Alta prioridad**: Puerto 80, 443 (HTTP/HTTPS)
   - **Media prioridad**: Puerto 22 (SSH)
   - **Baja prioridad**: Otros puertos

## 8. Monitoreo Diario para el Profesor

### Dashboard Simplificado
1. **Página principal del router**: `http://***********`
2. **Vista rápida**:
   - **Online Users**: Cuántos estudiantes conectados a "icfesmatematicas1"
   - **Traffic Statistics**: Uso de ancho de banda total
   - **System Status**: Estado general del router

### Monitoreo de Estudiantes
1. **Status → Online Users**
2. **Información visible**:
   - **IP Address**: Identificar dispositivos
   - **MAC Address**: Identificación única
   - **Connect Time**: Tiempo conectado
   - **Traffic**: Datos descargados/subidos

### Alertas y Notificaciones
1. **Advanced → System Tools → Email**
2. **Configurar notificaciones**:
   - **SMTP Server**: Gmail, Outlook, etc.
   - **Alertas por**: Conexión perdida, uso excesivo
   - **Frecuencia**: Inmediata o resumen diario

### Backup de Configuración
1. **System Tools → Backup & Restore**
2. **Recomendación**:
   - Backup antes de cada cambio importante
   - Guardar archivo en USB o computador
   - Nombre: "icfes-config-YYYY-MM-DD.bin"

## 9. Solución de Problemas Comunes

### Problema: Lentitud con muchos usuarios
**Solución**: 
- Reducir límite de ancho de banda por usuario
- Activar cache DNS local
- Optimizar reglas de firewall

### Problema: Sitios bloqueados incorrectamente
**Solución**:
- Revisar Domain Filtering rules
- Agregar excepciones específicas
- Verificar logs de acceso

### Problema: Conexión intermitente
**Solución**:
- Verificar cables de red
- Revisar configuración de Load Balancing
- Actualizar firmware del router

## 10. Funciones Adicionales Disponibles

### PPPoE Server (Servidor PPPoE)
1. **Advanced → Service → PPPoE Server**
2. **Útil para**:
   - Control de autenticación de usuarios
   - Facturación por tiempo de uso
   - Gestión centralizada de credenciales

### Dynamic DNS (DNS Dinámico)
1. **Advanced → Service → Dynamic DNS**
2. **Proveedores soportados**:
   - DynDNS, No-IP, Peanuthull, Comexe
   - Acceso remoto con dominio fijo

### IPTV Support
1. **Advanced → IPTV**
2. **Funciones**:
   - **IGMP Proxy**: Para streaming multicast
   - **IGMP Snooping**: Optimización de tráfico de video

### Backup y Restauración
1. **System Tools → Backup & Restore**
2. **Recomendaciones**:
   - Backup semanal automático
   - Guardar configuración antes de cambios importantes
   - Exportar configuración para replicar en otros routers

## 11. Configuración Paso a Paso para "icfesmatematicas1"

### Configuración Inicial Recomendada (30 minutos)

#### Paso 1: Acceso y Seguridad Básica
1. Conectar cable ethernet al puerto LAN
2. Ir a `http://***********`
3. **Cambiar contraseña admin**: System Tools → Account → Change Password
4. **Configurar SSID**: Advanced → Wireless → Basic Settings
   - SSID: "icfesmatematicas1" ✓ (ya configurado)
   - Security: WPA2-PSK con contraseña fuerte

#### Paso 2: Lista Blanca Básica (15 minutos)
1. **Advanced → Access Control → Web Content Filtering**
2. **Enable Web Content Filtering**: ✓
3. **Default Action**: Deny All
4. **Add Allowed Domains**:
   ```
   [IP-servidor-moodle]
   *.edu.co
   *.gov.co
   wikipedia.org
   translate.google.com
   ```

#### Paso 3: Control de Aplicaciones (5 minutos)
1. **Advanced → Access Control → Application Control**
2. **Block Applications**: ✓
   - IM (WhatsApp, Telegram)
   - P2P (Torrents)
   - Web SNS (Facebook, Instagram, TikTok)

#### Paso 4: Límites de Ancho de Banda (10 minutos)
1. **Advanced → Traffic Control → Bandwidth Control**
2. **Enable Bandwidth Control**: ✓
3. **Add Rule**:
   - IP Range: ***********-*************
   - Guaranteed: 1 Mbps
   - Limited: 3 Mbps

### Configuración Avanzada (Opcional)

#### Reservar IP para el Profesor
1. **Advanced → DHCP → Address Reservation**
2. **Add**: MAC de tu dispositivo → IP *************

#### Horarios de Acceso
1. **Advanced → Access Control → Time Range**
2. **Add Time Range**: "Horario-Clases"
   - Monday-Friday: 07:00-18:00
   - Saturday: 08:00-14:00

---

**Última actualización**: 2025-08-01
**Versión**: 2.1
**Estado**: Personalizado para ICFES Matemáticas - Un profesor, SSID único
**Fuente**: Documentación oficial TP-Link TL-ER604W
