# Tutorial TP-Link TL-ER604W para Entorno Educativo Moodle

## Información del Proyecto
- **Router**: TP-Link TL-ER604W SafeStream Wireless N Gigabit Broadband VPN Router
- **Entorno**: Servidor Ubuntu con Moodle
- **Usuarios**: 6 grupos de 30 estudiantes (180 total), con posibilidad de 70+ simultáneos
- **Objetivo**: Control de acceso a internet con lista blanca y funciones avanzadas

## Características Reales del TP-Link TL-ER604W

### Especificaciones Técnicas Oficiales
- **Tipo**: SafeStream Wireless N Gigabit Broadband VPN Router
- **Puertos**: 1 WAN Gigabit + 3 LAN Gigabit + 1 WAN/LAN Gigabit configurable
- **WiFi**: 802.11n 300Mbps (2.4GHz) con 2 antenas 5dBi desmontables
- **Sesiones simultáneas**: 10,000 sesiones concurrentes
- **NAT Throughput**: 160 Mbps
- **VPN**: 30 túneles IPsec + 8 PPTP + 8 L2TP simultáneos
- **IPsec VPN Throughput**: 38 Mbps
- **Multi-SSID**: Hasta 8 SSIDs diferentes
- **Memoria**: 64MB DDRII RAM + 8MB Flash
- **Alimentación**: 12VDC/1.0A (adaptador externo)
- **Protección**: 4KV contra rayos

## 1. Configuración Básica

### Acceso al Panel de Administración
1. Conectar por cable al puerto LAN
2. Abrir navegador: `http://***********` (IP por defecto)
3. Usuario: `admin` / Contraseña: `admin` (**CAMBIAR INMEDIATAMENTE**)

### Configuración Inicial de Red
```
Puerto WAN: Conexión principal a internet
Puertos LAN 1-3: Dispositivos cableados
Puerto WAN/LAN: Configurable según necesidad
WiFi: 300Mbps 802.11n (hasta 8 SSIDs)
```

### Funciones de Seguridad Disponibles
- **Application Control**: IM, P2P, Web IM, Web SNS, Web Media
- **Filtering**: MAC, URL/Keywords, Web Content (Java, ActiveX, Cookies)
- **Attack Defense**: TCP/UDP/ICMP Flood, TCP Scan, Ping Block
- **ARP Inspection**: GARP Packets, ARP Scanning, IP-MAC Binding

## 2. Lista Blanca de Sitios Web (Web Content Filtering)

### Configuración de Filtrado de Contenido
1. **Advanced → Access Control → Web Content Filtering**
2. **Crear política de filtrado**:
   - Nombre: "Estudiantes_Whitelist"
   - Acción: "Permit" (solo permitir)
   - Aplicar a: Todos los usuarios

### Control de Aplicaciones (Application Control)
El TL-ER604W incluye control nativo de:
- **IM**: Mensajería instantánea
- **P2P**: Torrents y descargas P2P
- **Web IM**: WhatsApp Web, Telegram Web
- **Web SNS**: Facebook, Twitter, Instagram
- **Web Media**: YouTube, streaming

### Sitios Permitidos (Lista Blanca)
```
# Sitios educativos esenciales
*.moodle.org
*.wikipedia.org
*.edu.co
*.gov.co

# Tu servidor Moodle local
*************  # (ajustar según tu IP del servidor)
localhost
tu-dominio-moodle.local

# Recursos educativos
*.khanacademy.org
*.coursera.org
*.edx.org
*.google.com/scholar
scholar.google.com

# Herramientas básicas
translate.google.com
drive.google.com
docs.google.com
```

### Configuración Paso a Paso
1. **Advanced → Access Control → Domain Filtering**
2. **Enable Domain Filtering**: ✓
3. **Default Action**: Deny (denegar todo por defecto)
4. **Add Domain Rules**:
   - Action: Allow
   - Domain: Agregar cada dominio de la lista

## 3. Gestión de Ancho de Banda y Tráfico

### Capacidad Real del Router
- **Sesiones simultáneas**: 10,000 (más que suficiente para 70+ usuarios)
- **NAT Throughput**: 160 Mbps total
- **Recomendación**: 2-3 Mbps por usuario para navegación fluida

### Configuración de Control de Ancho de Banda
1. **Advanced → Traffic Control → Bandwidth Control**
2. **Configurar límites por IP/rango**:
   - **Guarantee Bandwidth**: 1 Mbps mínimo por usuario
   - **Limited Bandwidth**: 3 Mbps máximo por usuario
   - **Time-scheduled Policy**: Horarios de clase

### Control de Sesiones
1. **Advanced → Traffic Control → Session Limit**
2. **Configurar límites**:
   - **IP-based Session Limit**: 50 sesiones por IP
   - **Previene**: Saturación por descargas masivas

## 4. Configuraciones Avanzadas Reales

### A. Multi-SSID (Hasta 8 Redes WiFi)
El TL-ER604W soporta hasta 8 SSIDs simultáneos:
1. **Advanced → Wireless → Multi-SSID**
2. **Configuración sugerida**:
   - **SSID1**: "Moodle-Estudiantes" (acceso restringido)
   - **SSID2**: "Moodle-Profesores" (acceso completo)
   - **SSID3**: "Invitados" (solo internet básico)
   - **SSID4-8**: Por grupos/aulas específicas

### B. Guest Network (Red de Invitados)
1. **Advanced → Wireless → Guest Network**
2. **Configurar**:
   - Acceso solo a internet
   - Sin acceso a red interna
   - Límite de tiempo de sesión
   - Ancho de banda limitado

### C. Programación de Acceso por Tiempo
1. **Advanced → Access Control → Time Range**
2. **Crear horarios**:
   - **Horario-Clases**: L-V 7:00-18:00, S 8:00-14:00
   - **Horario-Profesores**: L-D 6:00-22:00
   - **Horario-Mantenimiento**: D 2:00-6:00

### D. Monitoreo y Estadísticas
1. **Status → Traffic Statistics**: Uso de ancho de banda en tiempo real
2. **Status → Online Users**: Usuarios conectados actualmente
3. **Advanced → System Tools → Log**: Registro de eventos
4. **SNMP Support**: Para monitoreo externo

## 5. Funciones VPN Avanzadas

### Capacidades VPN del TL-ER604W
- **IPsec VPN**: 30 túneles simultáneos (LAN-to-LAN y Client-to-LAN)
- **PPTP VPN**: 8 túneles con cifrado MPPE
- **L2TP VPN**: 8 túneles con L2TP over IPsec
- **VPN Throughput**: 38 Mbps para IPsec

### Configuración VPN para Acceso Remoto
1. **Advanced → VPN → IPsec VPN**
2. **Para profesores remotos**:
   - Crear túnel Client-to-LAN
   - Cifrado: AES256
   - Autenticación: SHA1
   - Acceso completo a Moodle

### Configuración Site-to-Site
1. **Advanced → VPN → IPsec VPN**
2. **Para conectar sedes**:
   - Modo: LAN-to-LAN
   - Negociación: Main Mode
   - Perfect Forward Secrecy (PFS): Activado

## 6. Seguridad Avanzada Integrada

### Attack Defense (Defensa contra Ataques)
1. **Advanced → Security → Attack Defense**
2. **Activar protecciones**:
   - **TCP/UDP/ICMP Flood Defense**: Contra ataques DDoS
   - **Block TCP Scan**: Stealth FIN/Xmas/Null
   - **Block Ping from WAN**: Ocultar router desde internet
   - **CSRF Defense**: Protección contra falsificación de solicitudes

### Control de Aplicaciones (Un Clic)
1. **Advanced → Access Control → Application Control**
2. **Bloquear con un clic**:
   - **IM**: WhatsApp, Telegram, Skype
   - **P2P**: BitTorrent, eMule, Ares
   - **Web IM**: Versiones web de mensajería
   - **Web SNS**: Facebook, Instagram, Twitter
   - **Web Media**: YouTube, Netflix, Spotify

### IP-MAC Binding (Seguridad de Red)
1. **Advanced → Security → ARP Inspection**
2. **Configurar**:
   - **IP-MAC Binding**: Asociar IPs fijas a MACs específicas
   - **ARP Scanning**: Detectar dispositivos no autorizados
   - **GARP Packets**: Prevenir suplantación ARP

## 7. Configuración Específica para Moodle

### Port Forwarding para Servidor Moodle
1. **Advanced → NAT → Virtual Server**
2. **Configurar reglas**:
   - **HTTP**: External Port 80 → Internal Port 80 → IP Servidor Moodle
   - **HTTPS**: External Port 443 → Internal Port 443 → IP Servidor Moodle
   - **SSH**: External Port 22 → Internal Port 22 → IP Servidor Moodle (opcional)

### Optimización para 70+ Usuarios en Moodle
1. **Advanced → Traffic Control → Session Limit**
2. **Configurar límites**:
   - **Per IP Session Limit**: 100 sesiones por IP
   - **Total Session Limit**: 5000 sesiones totales
   - **Timeout**: 1800 segundos (30 minutos)

### Priorización de Tráfico Moodle
1. **Advanced → Traffic Control → Bandwidth Control**
2. **Crear reglas de prioridad**:
   - **Alta prioridad**: Puerto 80, 443 (HTTP/HTTPS)
   - **Media prioridad**: Puerto 22 (SSH)
   - **Baja prioridad**: Otros puertos

## 8. Monitoreo y Mantenimiento

### Dashboard de Monitoreo
- **Usuarios conectados**: Status → Online Users
- **Uso de ancho de banda**: Status → Traffic Statistics
- **Estado de WAN**: Status → WAN Status

### Alertas Automáticas
- **Email notifications** para:
  - Conexión WAN caída
  - Uso excesivo de ancho de banda
  - Intentos de acceso no autorizados

### Backup de Configuración
1. **System Tools → Backup & Restore**
2. **Backup automático**: Semanal
3. **Guardar en**: USB o servidor FTP

## 9. Solución de Problemas Comunes

### Problema: Lentitud con muchos usuarios
**Solución**: 
- Reducir límite de ancho de banda por usuario
- Activar cache DNS local
- Optimizar reglas de firewall

### Problema: Sitios bloqueados incorrectamente
**Solución**:
- Revisar Domain Filtering rules
- Agregar excepciones específicas
- Verificar logs de acceso

### Problema: Conexión intermitente
**Solución**:
- Verificar cables de red
- Revisar configuración de Load Balancing
- Actualizar firmware del router

## 10. Funciones Adicionales Disponibles

### PPPoE Server (Servidor PPPoE)
1. **Advanced → Service → PPPoE Server**
2. **Útil para**:
   - Control de autenticación de usuarios
   - Facturación por tiempo de uso
   - Gestión centralizada de credenciales

### Dynamic DNS (DNS Dinámico)
1. **Advanced → Service → Dynamic DNS**
2. **Proveedores soportados**:
   - DynDNS, No-IP, Peanuthull, Comexe
   - Acceso remoto con dominio fijo

### IPTV Support
1. **Advanced → IPTV**
2. **Funciones**:
   - **IGMP Proxy**: Para streaming multicast
   - **IGMP Snooping**: Optimización de tráfico de video

### Backup y Restauración
1. **System Tools → Backup & Restore**
2. **Recomendaciones**:
   - Backup semanal automático
   - Guardar configuración antes de cambios importantes
   - Exportar configuración para replicar en otros routers

---

**Última actualización**: 2025-08-01
**Versión**: 2.0
**Estado**: Actualizado con especificaciones oficiales TP-Link
**Fuente**: Documentación oficial TP-Link TL-ER604W
